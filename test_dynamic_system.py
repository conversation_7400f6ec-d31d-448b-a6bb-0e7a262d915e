#!/usr/bin/env python3
"""
Test script for the Dynamic Query System
Tests various types of questions about county data
"""

import asyncio
import sys
import os

# Add the package to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accela_knowledge_base.core.config import Config
from accela_knowledge_base.core.dynamic_orchestrator import DynamicOrchestrator


async def test_dynamic_queries():
    """Test various dynamic queries"""
    
    # Initialize system
    config = Config.from_env()
    orchestrator = DynamicOrchestrator(config)
    
    # Test queries covering different aspects
    test_queries = [
        # Fee calculation questions
        "Show me fee calculation differences between counties",
        "How do different counties handle permit fees?",
        "Which county has the most efficient fee processing?",
        
        # Workflow questions
        "Compare inspection workflows across counties",
        "How do counties handle permit approval processes?",
        "What are the differences in application review procedures?",
        
        # Technical implementation questions
        "Show me email notification implementations",
        "How do counties integrate with external systems?",
        "What are the best practices for error handling?",
        
        # General questions
        "Which county has the most comprehensive implementation?",
        "What are common patterns across all counties?",
        "How do counties handle document management?",
        
        # Specific technical questions
        "Show me JavaScript functions for permit processing",
        "How do counties implement conditional logic?",
        "What database queries are used for reporting?"
    ]
    
    print("🚀 Testing Dynamic Query System")
    print("=" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: {query}")
        print("-" * 40)
        
        try:
            # Process query
            result = await orchestrator.orchestrate(query)
            
            # Display results
            print(f"✅ Intent: {result.query_analysis.intent}")
            print(f"📊 Analysis Type: {result.query_analysis.analysis_type}")
            print(f"🏛️ Counties: {[r.county for r in result.county_results]}")
            print(f"🎯 Confidence: {result.confidence:.1%}")
            print(f"⏱️ Processing Time: {result.processing_time:.2f}s")
            
            # Show key findings
            if result.county_results:
                print(f"🔍 Key Findings:")
                for county_result in result.county_results[:2]:  # Show top 2
                    print(f"  • {county_result.county}: {len(county_result.strengths)} strengths, {len(county_result.code_examples)} code examples")
            
            # Show comparison if available
            if result.comparison_result:
                print(f"⚖️ Comparison: {len(result.comparison_result.similarities)} similarities, {len(result.comparison_result.differences)} differences")
            
            print(f"📝 Response Length: {len(result.markdown_response)} characters")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print()
    
    print("🎉 Dynamic Query System Testing Complete!")


async def test_specific_query():
    """Test a specific query in detail"""
    
    config = Config.from_env()
    orchestrator = DynamicOrchestrator(config)
    
    query = "Show me fee calculation differences between counties"
    
    print(f"🔍 Detailed Test: {query}")
    print("=" * 60)
    
    try:
        result = await orchestrator.orchestrate(query)
        
        print(f"📋 Query Analysis:")
        print(f"  Intent: {result.query_analysis.intent}")
        print(f"  Analysis Type: {result.query_analysis.analysis_type}")
        print(f"  Entities: {result.query_analysis.entities}")
        print(f"  Specific Aspects: {result.query_analysis.specific_aspects}")
        print(f"  Comparison Requested: {result.query_analysis.comparison_requested}")
        
        print(f"\n🏛️ County Results ({len(result.county_results)}):")
        for county_result in result.county_results:
            print(f"  • {county_result.county}:")
            print(f"    - Confidence: {county_result.confidence:.2f}")
            print(f"    - Strengths: {len(county_result.strengths)}")
            print(f"    - Code Examples: {len(county_result.code_examples)}")
            print(f"    - Recommendations: {len(county_result.recommendations)}")
        
        if result.comparison_result:
            print(f"\n⚖️ Comparison Result:")
            print(f"  - Similarities: {len(result.comparison_result.similarities)}")
            print(f"  - Differences: {len(result.comparison_result.differences)}")
            print(f"  - Best Practices: {len(result.comparison_result.best_practices)}")
            print(f"  - Summary: {result.comparison_result.summary[:100]}...")
        
        print(f"\n📝 Generated Response Preview:")
        print(result.markdown_response[:500] + "..." if len(result.markdown_response) > 500 else result.markdown_response)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


async def test_system_status():
    """Test system status"""
    
    config = Config.from_env()
    orchestrator = DynamicOrchestrator(config)
    
    print("🔧 System Status Check")
    print("=" * 30)
    
    status = orchestrator.get_status()
    
    print(f"Status: {status['status']}")
    print(f"LLM Enabled: {status['llm_enabled']}")
    print(f"Components: {status['components']}")
    print(f"Capabilities: {status['capabilities']}")


if __name__ == "__main__":
    print("🧪 Dynamic Query System Test Suite")
    print("=" * 40)
    
    # Run tests
    asyncio.run(test_system_status())
    print()
    asyncio.run(test_specific_query())
    print()
    asyncio.run(test_dynamic_queries())
