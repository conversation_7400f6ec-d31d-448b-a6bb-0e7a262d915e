"""
Agentic endpoints for multi-agent orchestration
"""

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime
import markdown

from ...core.models import OrchestrationRequest
from ...core.logging import get_logger
from ...core.query_processor import NaturalLanguageQueryProcessor, MarkdownFormatter

router = APIRouter()
logger = get_logger("agentic")


class AgenticQueryRequest(BaseModel):
    query: str
    use_case: Optional[str] = None
    target_counties: Optional[List[str]] = None
    constraints: Dict[str, Any] = {}
    priority: int = 1
    output_format: str = "markdown"  # "json" or "markdown"


class AgenticQueryResponse(BaseModel):
    request_id: str
    query: str
    best_implementation: Dict[str, Any]
    alternatives: List[Dict[str, Any]]
    synthesis: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str]
    processing_time: float
    markdown_output: Optional[str] = None


@router.post("/query", response_model=AgenticQueryResponse)
async def agentic_query(request: AgenticQueryRequest, app_request: Request):
    """Process natural language query using multi-agent orchestration"""

    orchestrator = getattr(app_request.app.state, 'orchestrator', None)
    if not orchestrator:
        raise HTTPException(status_code=500, detail="Orchestrator not initialized")

    start_time = datetime.now()

    # Process natural language query
    query_processor = NaturalLanguageQueryProcessor()
    processed_query = query_processor.process_query(request.query)

    # Use processed information or fallback to request parameters
    use_case = request.use_case or processed_query['use_case']
    target_counties = request.target_counties or processed_query['target_counties']
    enhanced_query = processed_query['enhanced_query']

    # Create orchestration request
    orchestration_request = OrchestrationRequest(
        request_id=str(uuid.uuid4()),
        query=enhanced_query,
        use_case=use_case,
        target_counties=target_counties,
        constraints=request.constraints,
        priority=request.priority
    )

    try:
        # Execute orchestration
        result = await orchestrator.orchestrate(orchestration_request)

        processing_time = (datetime.now() - start_time).total_seconds()

        # Generate markdown output if requested
        markdown_output = None
        if request.output_format == "markdown":
            # Use detailed formatter for better code analysis
            from ...core.detailed_markdown_formatter import DetailedMarkdownFormatter
            detailed_formatter = DetailedMarkdownFormatter()
            markdown_output = detailed_formatter.format_detailed_response(processed_query, result)

        return AgenticQueryResponse(
            request_id=result.request_id,
            query=request.query,  # Return original query
            best_implementation=result.best_implementation,
            alternatives=result.alternatives,
            synthesis=result.synthesis,
            confidence=result.confidence,
            reasoning=result.reasoning,
            recommendations=result.recommendations,
            processing_time=processing_time,
            markdown_output=markdown_output
        )

    except Exception as e:
        logger.error(f"Agentic query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Orchestration failed: {str(e)}")


@router.get("/status")
async def get_agentic_status(request: Request):
    """Get status of the agentic system"""
    
    try:
        orchestrator = getattr(request.app.state, 'orchestrator', None)
        knowledge_graph = getattr(request.app.state, 'knowledge_graph', None)
        
        if not orchestrator or not knowledge_graph:
            return {"status": "not_ready", "reason": "System not initialized"}
        
        # Get graph stats
        graph_stats = knowledge_graph.get_stats()
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "graph_stats": graph_stats,
            "agents": {
                "analyzer": "ready",
                "comparator": "ready", 
                "recommender": "ready",
                "synthesizer": "ready"
            },
            "llm_enabled": request.app.state.config.llm_enabled
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@router.post("/query/markdown", response_class=HTMLResponse)
async def agentic_query_markdown(request: AgenticQueryRequest, app_request: Request):
    """Process natural language query and return HTML-formatted markdown response"""

    # Force markdown output
    request.output_format = "markdown"

    # Get the JSON response
    json_response = await agentic_query(request, app_request)

    if not json_response.markdown_output:
        raise HTTPException(status_code=500, detail="Failed to generate markdown output")

    # Convert markdown to HTML
    html_content = markdown.markdown(
        json_response.markdown_output,
        extensions=['tables', 'fenced_code', 'toc']
    )

    # Wrap in a nice HTML template
    full_html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Accela Implementation Analysis</title>
    <meta charset="utf-8">
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }}
        .container {{
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-bottom: 1px solid #ecf0f1; padding-bottom: 5px; }}
        h3 {{ color: #7f8c8d; }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        th {{
            background-color: #3498db;
            color: white;
        }}
        tr:nth-child(even) {{
            background-color: #f2f2f2;
        }}
        code {{
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }}
        pre {{
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }}
        .confidence-high {{ color: #27ae60; font-weight: bold; }}
        .confidence-medium {{ color: #f39c12; font-weight: bold; }}
        .confidence-low {{ color: #e74c3c; font-weight: bold; }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        .query-info {{
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        {html_content}
        <div class="footer">
            <p><em>Generated by Accela Knowledge Base - Agentic AI System</em></p>
            <p><strong>Processing Time:</strong> {json_response.processing_time:.2f} seconds |
               <strong>Confidence:</strong>
               <span class="confidence-{'high' if json_response.confidence > 0.8 else 'medium' if json_response.confidence > 0.6 else 'low'}">
                   {json_response.confidence:.1%}
               </span>
            </p>
        </div>
    </div>
</body>
</html>
"""

    return HTMLResponse(content=full_html)


class NaturalLanguageQueryRequest(BaseModel):
    query: str
    counties: Optional[str] = None  # Comma-separated county names


@router.post("/ask", response_class=HTMLResponse)
async def natural_language_ask(request: NaturalLanguageQueryRequest, app_request: Request):
    """Simple natural language interface - just ask a question about Accela implementations with detailed code analysis"""

    # Parse counties if provided
    target_counties = None
    if request.counties:
        target_counties = [county.strip().lower().replace(' ', '_') for county in request.counties.split(',')]

    # Create agentic query request
    agentic_request = AgenticQueryRequest(
        query=request.query,
        target_counties=target_counties,
        output_format="markdown"
    )

    # Process through the markdown endpoint
    return await agentic_query_markdown(agentic_request, app_request)
